# Agente LightRAG para AWS SageMaker

Este proyecto implementa un agente inteligente que utiliza LightRAG (Retrieval-Augmented Generation) integrado con Google ADK y desplegable en AWS SageMaker.

## 🚀 Características

- **LightRAG**: Framework avanzado de RAG con base de datos vectorial y de grafos
- **Google ADK**: Integración con el Agent Development Kit de Google
- **AWS SageMaker**: Despliegue escalable en la nube
- **Bases de Datos**: Soporte para ChromaDB (vectorial) y Neo4j (grafos)
- **Múltiples Modos**: Búsqueda naive, local, global e híbrida

## 📁 Estructura del Proyecto

```
agent-v2/
├── agent/                  # Código principal del agente
│   ├── config/            # Configuraciones
│   ├── lightrag_agent.py  # Agente principal
│   ├── adk_integration.py # Integración con Google ADK
│   └── main.py           # Punto de entrada
├── docs/                  # Documentos para la base de conocimiento
│   ├── api/              # Documentación de APIs
│   ├── guides/           # Guías de usuario
│   ├── technical/        # Documentación técnica
│   └── examples/         # Ejemplos de uso
├── sagemaker/            # Archivos para despliegue en SageMaker
│   ├── Dockerfile        # Imagen Docker
│   ├── inference.py      # Script de inferencia
│   └── requirements.txt  # Dependencias
├── deployment/           # Scripts de despliegue
│   ├── deploy_sagemaker.py  # Despliegue automático
│   └── test_endpoint.py     # Pruebas del endpoint
└── test_agent.py        # Script de pruebas locales
```

## 🛠️ Instalación

### Prerrequisitos

- Python 3.12+
- Conda
- Docker (para despliegue)
- AWS CLI configurado con perfil "IA"
- Cuenta de OpenAI con API key

### Instalación Local

1. **Instalar dependencias**:
```bash
source ~/miniconda3/bin/activate
python -m pip install lightrag chromadb neo4j openai
```

2. **Configurar variables de entorno**:
```bash
cp .env.example .env
# Editar .env con tus credenciales
```

3. **Probar la instalación**:
```bash
python test_agent.py
```

## 🚀 Uso

### Modo Interactivo Local

```bash
python -m agent.main
```

### Modo ADK

```bash
python -m agent.main adk
```

### Consultas Programáticas

```python
import asyncio
from agent import get_agent

async def main():
    agent = get_agent()
    
    # Cargar documentos
    await agent.load_documents()
    
    # Realizar consulta
    response = await agent.query("¿Qué es LightRAG?", mode="hybrid")
    print(response)

asyncio.run(main())
```

## 🌐 Despliegue en AWS SageMaker

### 1. Preparar el Entorno

```bash
# Configurar AWS CLI con perfil IA
aws configure --profile IA

# Verificar configuración
aws sts get-caller-identity --profile IA
```

### 2. Desplegar

```bash
python deployment/deploy_sagemaker.py \
    --profile IA \
    --region us-east-1 \
    --role-arn arn:aws:iam::YOUR-ACCOUNT:role/SageMakerRole \
    --instance-type ml.t3.medium
```

### 3. Probar el Endpoint

```bash
# Health check
python deployment/test_endpoint.py \
    --endpoint-name lightrag-agent-endpoint-TIMESTAMP \
    --profile IA

# Suite completa de pruebas
python deployment/test_endpoint.py \
    --endpoint-name lightrag-agent-endpoint-TIMESTAMP \
    --profile IA \
    --test-suite
```

## 📊 Modos de Búsqueda

- **naive**: Búsqueda simple por similitud
- **local**: Búsqueda en contexto local
- **global**: Búsqueda en todo el grafo de conocimiento
- **hybrid**: Combinación de búsqueda local y global (recomendado)

## 🔧 Configuración

### Variables de Entorno

```bash
# OpenAI
OPENAI_API_KEY=your_api_key

# Neo4j (opcional)
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password

# AWS SageMaker
SAGEMAKER_ROLE_ARN=arn:aws:iam::account:role/SageMakerRole
AWS_PROFILE=IA
AWS_REGION=us-east-1
```

## 📚 API del Endpoint

### POST /invocations

Consulta principal al agente:

```json
{
  "question": "¿Qué es LightRAG?",
  "mode": "hybrid",
  "use_adk": false
}
```

### POST /load_documents

Cargar documentos:

```json
{
  "docs_path": "/path/to/documents"
}
```

### GET /graph_info

Información del grafo de conocimiento.

### GET /ping

Health check.

## 🧪 Pruebas

### Pruebas Locales

```bash
# Pruebas básicas
python test_agent.py

# Modo interactivo
python -m agent.main
```

---

**Nota**: Este agente está optimizado para uso con el perfil AWS "IA" y documentos en español.
