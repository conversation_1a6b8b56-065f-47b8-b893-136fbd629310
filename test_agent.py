#!/usr/bin/env python3
"""
Script de prueba para el agente LightRAG
"""
import asyncio
import os
import sys
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent))

from agent.main import initialize_agent, query_agent


async def test_basic_functionality():
    """
    Prueba la funcionalidad básica del agente
    """
    print("=== Prueba del Agente LightRAG ===\n")
    
    # Verificar variables de entorno
    print("1. Verificando configuración...")
    
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  OPENAI_API_KEY no configurada. Usando configuración de prueba.")
        # Para pruebas, podemos usar un modelo local o mock
    else:
        print("✅ OPENAI_API_KEY configurada")
    
    # Verificar directorio de documentos
    docs_dir = Path("docs")
    if docs_dir.exists():
        doc_files = list(docs_dir.rglob("*.md")) + list(docs_dir.rglob("*.txt"))
        print(f"✅ Directorio docs encontrado con {len(doc_files)} archivos")
    else:
        print("⚠️  Directorio docs no encontrado")
    
    print("\n2. Inicializando agente...")
    
    try:
        # Inicializar agente
        success = await initialize_agent()
        
        if success:
            print("✅ Agente inicializado correctamente")
        else:
            print("❌ Error inicializando agente")
            return False
        
        print("\n3. Probando consultas...")
        
        # Pruebas de consulta
        test_questions = [
            "¿Qué es este agente?",
            "¿Cómo funciona LightRAG?",
            "¿Cuáles son las funcionalidades principales?",
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\nPregunta {i}: {question}")
            
            try:
                response = await query_agent(question, "hybrid")
                print(f"Respuesta: {response[:200]}...")
                print("✅ Consulta exitosa")
                
            except Exception as e:
                print(f"❌ Error en consulta: {e}")
        
        print("\n=== Pruebas completadas ===")
        return True
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {e}")
        return False


async def test_different_modes():
    """
    Prueba diferentes modos de consulta
    """
    print("\n=== Prueba de Modos de Consulta ===\n")
    
    question = "¿Qué es la arquitectura del agente?"
    modes = ["naive", "local", "global", "hybrid"]
    
    for mode in modes:
        print(f"Probando modo '{mode}'...")
        
        try:
            response = await query_agent(question, mode)
            print(f"✅ Modo {mode}: {len(response)} caracteres de respuesta")
            
        except Exception as e:
            print(f"❌ Error en modo {mode}: {e}")


def create_sample_documents():
    """
    Crea documentos de ejemplo si no existen
    """
    docs_dir = Path("docs")
    
    if not docs_dir.exists():
        print("Creando documentos de ejemplo...")
        docs_dir.mkdir(exist_ok=True)
        
        # Ya tenemos algunos documentos creados anteriormente
        print("✅ Documentos de ejemplo disponibles")
    else:
        print("✅ Directorio de documentos ya existe")


async def main():
    """Función principal de pruebas"""
    print("Iniciando pruebas del agente LightRAG...\n")
    
    # Crear documentos de ejemplo si es necesario
    create_sample_documents()
    
    # Ejecutar pruebas básicas
    basic_success = await test_basic_functionality()
    
    if basic_success:
        # Ejecutar pruebas de modos
        await test_different_modes()
    
    print("\n🎉 Pruebas finalizadas")


if __name__ == "__main__":
    asyncio.run(main())
