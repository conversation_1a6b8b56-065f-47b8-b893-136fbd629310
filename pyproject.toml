[project]
# Project metadata. Available keys are documented at:
# https://packaging.python.org/en/latest/specifications/declaring-project-metadata

name = "google-adk"
description = "Agent Development Kit"
readme = "README.md"
requires-python = ">=3.9"
license = { file = "LICENSE" }
authors = [{ name = "Google LLC", email = "<EMAIL>" }]
classifiers = [ # List of https://pypi.org/classifiers/
  "Typing :: Typed",
  "Intended Audience :: Developers",
  "Intended Audience :: Science/Research",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Operating System :: OS Independent",
  "Topic :: Software Development :: Libraries :: Python Modules",
  "License :: OSI Approved :: Apache Software License",
]
dependencies = [
  # go/keep-sorted start
  "PyYAML>=6.0.2",                                  # For APIHubToolset.
  "anyio>=4.9.0;python_version>='3.10'",            # For MCP Session Manager
  "authlib>=1.5.1",                                 # For RestAPI Tool
  "click>=8.1.8",                                   # For CLI tools
  "fastapi>=0.115.0",                               # FastAPI framework
  "google-api-python-client>=2.157.0",              # Google API client discovery
  "google-cloud-aiplatform[agent_engines]>=1.95.1", # For VertexAI integrations, e.g. example store.
  "google-cloud-secret-manager>=2.22.0",            # Fetching secrets in RestAPI Tool
  "google-cloud-speech>=2.30.0",                    # For Audio Transcription
  "google-cloud-storage>=2.18.0, <3.0.0",           # For GCS Artifact service
  "google-genai>=1.21.1",                           # Google GenAI SDK
  "graphviz>=0.20.2",                               # Graphviz for graph rendering
  "mcp>=1.8.0;python_version>='3.10'",              # For MCP Toolset
  "opentelemetry-api>=1.31.0",                      # OpenTelemetry
  "opentelemetry-exporter-gcp-trace>=1.9.0",
  "opentelemetry-sdk>=1.31.0",
  "pydantic>=2.0, <3.0.0",                          # For data validation/models
  "python-dateutil>=2.9.0.post0",                   # For Vertext AI Session Service
  "python-dotenv>=1.0.0",                           # To manage environment variables
  "requests>=2.32.4",
  "sqlalchemy>=2.0",                                # SQL database ORM
  "starlette>=0.46.2",                              # For FastAPI CLI
  "tenacity>=8.0.0",                                # For Retry management
  "typing-extensions>=4.5, <5",
  "tzlocal>=5.3",                                   # Time zone utilities
  "uvicorn>=0.34.0",                                # ASGI server for FastAPI
  "websockets>=15.0.1",                             # For BaseLlmFlow
  # go/keep-sorted end
]
dynamic = ["version"]

[project.urls]
homepage = "https://google.github.io/adk-docs/"
repository = "https://github.com/google/adk-python"
changelog = "https://github.com/google/adk-python/blob/main/CHANGELOG.md"
documentation = "https://google.github.io/adk-docs/"

[project.scripts]
adk = "google.adk.cli:main"

[project.optional-dependencies]

dev = [
  # go/keep-sorted start
  "flit>=3.10.0",
  "isort>=6.0.0",
  "mypy>=1.15.0",
  "pyink>=24.10.0",
  "pylint>=2.6.0",
  # go/keep-sorted end
]

a2a = [
  # go/keep-sorted start
  "a2a-sdk>=0.2.7;python_version>='3.10'"
  # go/keep-sorted end
]

eval = [
  # go/keep-sorted start
  "google-cloud-aiplatform[evaluation]>=1.100.0",
  "pandas>=2.2.3",
  "tabulate>=0.9.0",
  "rouge-score>=0.1.2",
  # go/keep-sorted end
]

test = [
  # go/keep-sorted start
  "anthropic>=0.43.0",               # For anthropic model tests
  "langchain-community>=0.3.17",
  # langgraph 0.5 removed langgraph.graph.graph which we depend on
  "langgraph>=0.2.60, <= 0.4.10",    # For LangGraphAgent
  "litellm>=1.71.2",                 # For LiteLLM tests
  "llama-index-readers-file>=0.4.0", # For retrieval tests
  "pytest-asyncio>=0.25.0",
  "pytest-mock>=3.14.0",
  "pytest-xdist>=3.6.1",
  "pytest>=8.3.4",
  # go/keep-sorted end
]

docs = [
  "autodoc_pydantic",
  "furo",
  "myst-parser",
  "sphinx",
  "sphinx-autodoc-typehints",
  "sphinx-rtd-theme",
]

# Optional extensions
extensions = [
  "anthropic>=0.43.0",                    # For anthropic model support
  "beautifulsoup4>=3.2.2",                # For load_web_page tool.
  "crewai[tools];python_version>='3.10'", # For CrewaiTool
  "docker>=7.0.0",                        # For ContainerCodeExecutor
  "langgraph>=0.2.60",                    # For LangGraphAgent
  "litellm>=1.63.11",                     # For LiteLLM support
  "llama-index-readers-file>=0.4.0",      # For retrieval using LlamaIndex.
  "lxml>=5.3.0",                          # For load_web_page tool.
  "toolbox-core>=0.1.0",                  # For tools.toolbox_toolset.ToolboxToolset
]


[tool.pyink]
# Format py files following Google style-guide
line-length = 80
unstable = true
pyink-indentation = 2
pyink-use-majority-quotes = true
pyink-annotation-pragmas = [
  "noqa",
  "pylint:",
  "type: ignore",
  "pytype:",
  "mypy:",
  "pyright:",
  "pyre-",
]


[build-system]
# Build system specify which backend is used to build/install the project (flit,
# poetry, setuptools,...). All backends are supported by `pip install`
requires = ["flit_core >=3.8,<4"]
build-backend = "flit_core.buildapi"


[tool.flit.sdist]
include = ['src/**/*', 'README.md', 'pyproject.toml', 'LICENSE']
exclude = ['src/**/*.sh']


[tool.flit.module]
name = "google.adk"
include = ["py.typed"]


[tool.isort]
profile = "google"
single_line_exclusions = []
line_length = 200                  # Prevent line wrap flickering.
known_third_party = ["google.adk"]


[tool.pytest.ini_options]
testpaths = ["tests"]
asyncio_default_fixture_loop_scope = "function"
asyncio_mode = "auto"


[tool.mypy]
python_version = "3.9"
exclude = "tests/"
plugins = ["pydantic.mypy"]
# Start with non-strict mode, and swtich to strict mode later.
# strict = true
disable_error_code = ["import-not-found", "import-untyped", "unused-ignore"]
follow_imports = "skip"
