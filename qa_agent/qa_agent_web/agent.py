# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Agente Q&A con RAG para interfaz web del ADK
"""

# Importar el agente desde el directorio src
import sys
import asyncio
from pathlib import Path

# Agregar el directorio src al path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from agent import root_agent, ensure_rag_initialized

# Inicializar el sistema RAG al cargar el módulo
try:
    # Ejecutar la inicialización de manera síncrona
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(ensure_rag_initialized())
    loop.close()
except Exception as e:
    print(f"⚠️  Advertencia: No se pudo inicializar el sistema RAG: {e}")
    print("   El agente funcionará sin capacidades RAG.")

# Hacer el agente disponible para el ADK web server
agent = root_agent
