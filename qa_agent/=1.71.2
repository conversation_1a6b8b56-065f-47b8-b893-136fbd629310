Collecting litellm
  Downloading litellm-1.74.0.post1.tar.gz (9.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 9.0/9.0 MB 146.9 kB/s eta 0:00:00
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting aiohttp>=3.10 (from litellm)
  Using cached aiohttp-3.12.13-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.6 kB)
Requirement already satisfied: click in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from litellm) (8.2.1)
Requirement already satisfied: httpx>=0.23.0 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from litellm) (0.28.1)
Requirement already satisfied: importlib-metadata>=6.8.0 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from litellm) (8.7.0)
Collecting jinja2<4.0.0,>=3.1.2 (from litellm)
  Using cached jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)
Requirement already satisfied: jsonschema<5.0.0,>=4.22.0 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from litellm) (4.24.0)
Collecting openai>=1.68.2 (from litellm)
  Downloading openai-1.93.2-py3-none-any.whl.metadata (29 kB)
Requirement already satisfied: pydantic<3.0.0,>=2.5.0 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from litellm) (2.11.7)
Requirement already satisfied: python-dotenv>=0.2.0 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from litellm) (1.1.1)
Collecting tiktoken>=0.7.0 (from litellm)
  Downloading tiktoken-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)
Collecting tokenizers (from litellm)
  Using cached tokenizers-0.21.2-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Collecting MarkupSafe>=2.0 (from jinja2<4.0.0,>=3.1.2->litellm)
  Using cached MarkupSafe-3.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.0 kB)
Requirement already satisfied: attrs>=22.2.0 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (25.3.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (2025.4.1)
Requirement already satisfied: referencing>=0.28.4 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (0.36.2)
Requirement already satisfied: rpds-py>=0.7.1 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (0.26.0)
Requirement already satisfied: annotated-types>=0.6.0 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.5.0->litellm) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.5.0->litellm) (2.33.2)
Requirement already satisfied: typing-extensions>=4.12.2 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.5.0->litellm) (4.14.1)
Requirement already satisfied: typing-inspection>=0.4.0 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.5.0->litellm) (0.4.1)
Collecting aiohappyeyeballs>=2.5.0 (from aiohttp>=3.10->litellm)
  Using cached aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.1.2 (from aiohttp>=3.10->litellm)
  Using cached aiosignal-1.4.0-py3-none-any.whl.metadata (3.7 kB)
Collecting frozenlist>=1.1.1 (from aiohttp>=3.10->litellm)
  Using cached frozenlist-1.7.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (18 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp>=3.10->litellm)
  Using cached multidict-6.6.3-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl.metadata (5.3 kB)
Collecting propcache>=0.2.0 (from aiohttp>=3.10->litellm)
  Using cached propcache-0.3.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp>=3.10->litellm)
  Using cached yarl-1.20.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (73 kB)
Requirement already satisfied: idna>=2.0 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from yarl<2.0,>=1.17.0->aiohttp>=3.10->litellm) (3.10)
Requirement already satisfied: anyio in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from httpx>=0.23.0->litellm) (4.9.0)
Requirement already satisfied: certifi in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from httpx>=0.23.0->litellm) (2025.7.9)
Requirement already satisfied: httpcore==1.* in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from httpx>=0.23.0->litellm) (1.0.9)
Requirement already satisfied: h11>=0.16 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from httpcore==1.*->httpx>=0.23.0->litellm) (0.16.0)
Requirement already satisfied: zipp>=3.20 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from importlib-metadata>=6.8.0->litellm) (3.23.0)
Collecting distro<2,>=1.7.0 (from openai>=1.68.2->litellm)
  Downloading distro-1.9.0-py3-none-any.whl.metadata (6.8 kB)
Collecting jiter<1,>=0.4.0 (from openai>=1.68.2->litellm)
  Downloading jiter-0.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.2 kB)
Requirement already satisfied: sniffio in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from openai>=1.68.2->litellm) (1.3.1)
Collecting tqdm>4 (from openai>=1.68.2->litellm)
  Using cached tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
Collecting regex>=2022.1.18 (from tiktoken>=0.7.0->litellm)
  Using cached regex-2024.11.6-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (40 kB)
Requirement already satisfied: requests>=2.26.0 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from tiktoken>=0.7.0->litellm) (2.32.4)
Requirement already satisfied: charset_normalizer<4,>=2 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from requests>=2.26.0->tiktoken>=0.7.0->litellm) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from requests>=2.26.0->tiktoken>=0.7.0->litellm) (2.5.0)
Collecting huggingface-hub<1.0,>=0.16.4 (from tokenizers->litellm)
  Using cached huggingface_hub-0.33.2-py3-none-any.whl.metadata (14 kB)
Collecting filelock (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm)
  Using cached filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting fsspec>=2023.5.0 (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm)
  Using cached fsspec-2025.5.1-py3-none-any.whl.metadata (11 kB)
Requirement already satisfied: packaging>=20.9 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (25.0)
Requirement already satisfied: pyyaml>=5.1 in /home/<USER>/miniconda3/envs/qa_agent/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (6.0.2)
Collecting hf-xet<2.0.0,>=1.1.2 (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm)
  Using cached hf_xet-1.1.5-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (879 bytes)
Using cached jinja2-3.1.6-py3-none-any.whl (134 kB)
Using cached aiohttp-3.12.13-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)
Using cached multidict-6.6.3-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl (246 kB)
Using cached yarl-1.20.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (348 kB)
Using cached aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Using cached aiosignal-1.4.0-py3-none-any.whl (7.5 kB)
Using cached frozenlist-1.7.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (235 kB)
Using cached MarkupSafe-3.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (23 kB)
Downloading openai-1.93.2-py3-none-any.whl (755 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 755.1/755.1 kB 114.9 kB/s eta 0:00:00
Downloading distro-1.9.0-py3-none-any.whl (20 kB)
Downloading jiter-0.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (352 kB)
Using cached propcache-0.3.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (213 kB)
Downloading tiktoken-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 179.4 kB/s eta 0:00:00
Using cached regex-2024.11.6-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (792 kB)
Using cached tqdm-4.67.1-py3-none-any.whl (78 kB)
Using cached tokenizers-0.21.2-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.1 MB)
Using cached huggingface_hub-0.33.2-py3-none-any.whl (515 kB)
Using cached hf_xet-1.1.5-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.1 MB)
Using cached fsspec-2025.5.1-py3-none-any.whl (199 kB)
Using cached filelock-3.18.0-py3-none-any.whl (16 kB)
Building wheels for collected packages: litellm
  Building wheel for litellm (pyproject.toml): started
  Building wheel for litellm (pyproject.toml): finished with status 'done'
  Created wheel for litellm: filename=litellm-1.74.0.post1-py3-none-any.whl size=8544109 sha256=bf7c38cf371f7653fe1da0f83fc792fb1041280a46a907ff1eee184a17b64e6f
  Stored in directory: /home/<USER>/.cache/pip/wheels/5a/26/3b/3c07afb2df4acdf458b2444b80502ee03ff1b59b54eb9fb24d
Successfully built litellm
Installing collected packages: tqdm, regex, propcache, multidict, MarkupSafe, jiter, hf-xet, fsspec, frozenlist, filelock, distro, aiohappyeyeballs, yarl, tiktoken, jinja2, huggingface-hub, aiosignal, tokenizers, openai, aiohttp, litellm

Successfully installed MarkupSafe-3.0.2 aiohappyeyeballs-2.6.1 aiohttp-3.12.13 aiosignal-1.4.0 distro-1.9.0 filelock-3.18.0 frozenlist-1.7.0 fsspec-2025.5.1 hf-xet-1.1.5 huggingface-hub-0.33.2 jinja2-3.1.6 jiter-0.10.0 litellm-1.74.0.post1 multidict-6.6.3 openai-1.93.2 propcache-0.3.2 regex-2024.11.6 tiktoken-0.9.0 tokenizers-0.21.2 tqdm-4.67.1 yarl-1.20.1
