# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Agente Q&A con RAG usando Google ADK
"""

from .agent import root_agent, create_rag_agent, ensure_rag_initialized
from .database import db_manager
from .embeddings import embedding_generator
from .document_processor import document_processor

__all__ = [
    "root_agent", 
    "create_rag_agent", 
    "ensure_rag_initialized",
    "db_manager",
    "embedding_generator", 
    "document_processor"
]
