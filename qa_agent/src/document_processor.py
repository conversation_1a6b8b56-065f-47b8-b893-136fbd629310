# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Procesador de documentos para RAG
"""

import os
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging
import re

# Importaciones para procesamiento de documentos
try:
    import pypdf
    from docx import Document
    import tiktoken
except ImportError as e:
    logging.warning(f"Algunas dependencias de procesamiento no están disponibles: {e}")

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """Procesador de documentos para extraer y chunking de texto"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        Inicializar el procesador de documentos
        
        Args:
            chunk_size: Tamaño máximo de cada chunk en tokens
            chunk_overlap: Solapamiento entre chunks en tokens
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        
        # Inicializar tokenizer para contar tokens
        try:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")  # Encoding usado por GPT-3.5/4
        except:
            logger.warning("No se pudo cargar tiktoken, usando aproximación de tokens")
            self.tokenizer = None
    
    def count_tokens(self, text: str) -> int:
        """Contar tokens en un texto"""
        if self.tokenizer:
            return len(self.tokenizer.encode(text))
        else:
            # Aproximación: ~4 caracteres por token
            return len(text) // 4
    
    def load_document(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """
        Cargar un documento y extraer su contenido
        
        Args:
            file_path: Ruta al archivo
            
        Returns:
            Tupla con (contenido, metadatos)
        """
        file_extension = file_path.suffix.lower()
        metadata = {
            "filename": file_path.name,
            "file_size": file_path.stat().st_size,
            "file_type": file_extension
        }
        
        try:
            if file_extension == '.txt':
                content = self._load_txt(file_path)
            elif file_extension == '.pdf':
                content = self._load_pdf(file_path)
            elif file_extension in ['.docx', '.doc']:
                content = self._load_docx(file_path)
            elif file_extension == '.md':
                content = self._load_markdown(file_path)
            else:
                logger.warning(f"Tipo de archivo no soportado: {file_extension}")
                return "", metadata
            
            # Limpiar y normalizar el contenido
            content = self._clean_text(content)
            metadata["char_count"] = len(content)
            metadata["token_count"] = self.count_tokens(content)
            
            return content, metadata
            
        except Exception as e:
            logger.error(f"Error cargando documento {file_path}: {e}")
            return "", metadata
    
    def _load_txt(self, file_path: Path) -> str:
        """Cargar archivo de texto plano"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def _load_pdf(self, file_path: Path) -> str:
        """Cargar archivo PDF"""
        try:
            with open(file_path, 'rb') as f:
                reader = pypdf.PdfReader(f)
                content = ""
                for page in reader.pages:
                    content += page.extract_text() + "\n"
                return content
        except Exception as e:
            logger.error(f"Error leyendo PDF {file_path}: {e}")
            return ""
    
    def _load_docx(self, file_path: Path) -> str:
        """Cargar archivo Word"""
        try:
            doc = Document(file_path)
            content = ""
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            return content
        except Exception as e:
            logger.error(f"Error leyendo DOCX {file_path}: {e}")
            return ""
    
    def _load_markdown(self, file_path: Path) -> str:
        """Cargar archivo Markdown"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def _clean_text(self, text: str) -> str:
        """Limpiar y normalizar texto"""
        # Eliminar espacios en blanco excesivos
        text = re.sub(r'\s+', ' ', text)
        
        # Eliminar líneas vacías múltiples
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # Eliminar caracteres de control
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', text)
        
        return text.strip()
    
    def chunk_text(self, text: str, metadata: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Dividir texto en chunks con solapamiento
        
        Args:
            text: Texto a dividir
            metadata: Metadatos del documento
            
        Returns:
            Lista de chunks con metadatos
        """
        if not text.strip():
            return []
        
        metadata = metadata or {}
        chunks = []
        
        # Dividir por párrafos primero
        paragraphs = text.split('\n\n')
        current_chunk = ""
        current_tokens = 0
        chunk_index = 0
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            paragraph_tokens = self.count_tokens(paragraph)
            
            # Si el párrafo solo es muy grande, dividirlo por oraciones
            if paragraph_tokens > self.chunk_size:
                # Guardar chunk actual si existe
                if current_chunk:
                    chunks.append(self._create_chunk(current_chunk, chunk_index, metadata))
                    chunk_index += 1
                    current_chunk = ""
                    current_tokens = 0
                
                # Dividir párrafo grande por oraciones
                sentences = self._split_by_sentences(paragraph)
                for sentence in sentences:
                    sentence_tokens = self.count_tokens(sentence)
                    
                    if current_tokens + sentence_tokens > self.chunk_size and current_chunk:
                        chunks.append(self._create_chunk(current_chunk, chunk_index, metadata))
                        chunk_index += 1
                        
                        # Mantener solapamiento
                        overlap_text = self._get_overlap_text(current_chunk)
                        current_chunk = overlap_text + " " + sentence if overlap_text else sentence
                        current_tokens = self.count_tokens(current_chunk)
                    else:
                        current_chunk += " " + sentence if current_chunk else sentence
                        current_tokens += sentence_tokens
            else:
                # Verificar si agregar este párrafo excede el límite
                if current_tokens + paragraph_tokens > self.chunk_size and current_chunk:
                    chunks.append(self._create_chunk(current_chunk, chunk_index, metadata))
                    chunk_index += 1
                    
                    # Mantener solapamiento
                    overlap_text = self._get_overlap_text(current_chunk)
                    current_chunk = overlap_text + "\n\n" + paragraph if overlap_text else paragraph
                    current_tokens = self.count_tokens(current_chunk)
                else:
                    current_chunk += "\n\n" + paragraph if current_chunk else paragraph
                    current_tokens += paragraph_tokens
        
        # Agregar último chunk si existe
        if current_chunk.strip():
            chunks.append(self._create_chunk(current_chunk, chunk_index, metadata))
        
        logger.info(f"Texto dividido en {len(chunks)} chunks")
        return chunks
    
    def _split_by_sentences(self, text: str) -> List[str]:
        """Dividir texto por oraciones"""
        # Patrón simple para dividir por oraciones
        sentences = re.split(r'[.!?]+\s+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _get_overlap_text(self, text: str) -> str:
        """Obtener texto de solapamiento del final del chunk"""
        tokens = self.count_tokens(text)
        if tokens <= self.chunk_overlap:
            return text
        
        # Aproximar posición de inicio del solapamiento
        overlap_ratio = self.chunk_overlap / tokens
        start_pos = int(len(text) * (1 - overlap_ratio))
        
        # Buscar el inicio de una oración cerca de la posición calculada
        overlap_text = text[start_pos:]
        sentence_start = re.search(r'[.!?]\s+', overlap_text)
        if sentence_start:
            return overlap_text[sentence_start.end():]
        
        return overlap_text
    
    def _create_chunk(self, content: str, index: int, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Crear un chunk con metadatos"""
        return {
            "content": content.strip(),
            "chunk_index": index,
            "char_count": len(content),
            "token_count": self.count_tokens(content),
            "metadata": metadata.copy()
        }
    
    async def process_documents_folder(self, docs_folder: Path) -> List[Dict[str, Any]]:
        """
        Procesar todos los documentos en una carpeta
        
        Args:
            docs_folder: Ruta a la carpeta de documentos
            
        Returns:
            Lista de documentos procesados con chunks
        """
        if not docs_folder.exists():
            logger.error(f"La carpeta {docs_folder} no existe")
            return []
        
        processed_docs = []
        supported_extensions = {'.txt', '.pdf', '.docx', '.doc', '.md'}
        
        for file_path in docs_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                logger.info(f"Procesando documento: {file_path.name}")
                
                content, metadata = self.load_document(file_path)
                if content:
                    chunks = self.chunk_text(content, metadata)
                    processed_docs.append({
                        "filename": file_path.name,
                        "content": content,
                        "metadata": metadata,
                        "chunks": chunks
                    })
                    logger.info(f"Documento {file_path.name} procesado: {len(chunks)} chunks")
                else:
                    logger.warning(f"No se pudo extraer contenido de {file_path.name}")
        
        logger.info(f"Procesados {len(processed_docs)} documentos")
        return processed_docs


# Instancia global del procesador
document_processor = DocumentProcessor()
