# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Agente simple de Q&A usando Google ADK
"""

import os
from typing import Optional

from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from google.genai import types


def create_qa_agent(model_name: Optional[str] = None) -> Agent:
    """
    Crea un agente simple de Q&A.
    
    Args:
        model_name: Nombre del modelo a usar. Por defecto usa gemini-2.0-flash.
        
    Returns:
        Agent configurado para Q&A.
    """
    
    # Configurar modelo por defecto
    if model_name is None:
        model_name = "gemini-2.0-flash"
    
    # Determinar si usar LiteLlm o modelo directo
    if "/" in model_name:
        # Usar LiteLlm para modelos con formato provider/model
        model = LiteLlm(model=model_name)
    else:
        # Usar modelo directo para modelos de Gemini
        model = model_name
    
    # Crear agente de Q&A
    qa_agent = Agent(
        model=model,
        name="qa_agent",
        description="Agente simple de preguntas y respuestas que proporciona información útil y precisa.",
        instruction="""
        Eres un asistente de inteligencia artificial especializado en responder preguntas de manera clara y precisa.
        
        Tus responsabilidades:
        1. Responder preguntas de los usuarios de forma directa y útil
        2. Proporcionar información precisa y bien estructurada
        3. Admitir cuando no sabes algo en lugar de inventar información
        4. Ser conciso pero completo en tus respuestas
        5. Mantener un tono profesional y amigable
        
        Cuando respondas:
        - Sé claro y directo
        - Usa ejemplos cuando sea útil
        - Estructura tu respuesta de manera lógica
        - Si la pregunta es ambigua, pide aclaración
        """,
        generate_content_config=types.GenerateContentConfig(
            temperature=0.7,
            max_output_tokens=1024,
            safety_settings=[
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HARASSMENT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
            ]
        ),
    )
    
    return qa_agent


# Instancia por defecto del agente
root_agent = create_qa_agent()
