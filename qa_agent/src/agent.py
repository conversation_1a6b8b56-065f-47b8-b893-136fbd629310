# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Agente Q&A con RAG usando Google ADK
"""

import os
import sys
from typing import List, Optional, Dict, Any
import asyncio
import logging
from pathlib import Path

from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools.base_tool import BaseTool
from google.genai import types

# Importar módulos RAG
from database import db_manager
from embeddings import embedding_generator
from document_processor import document_processor

logger = logging.getLogger(__name__)


class RAGSearchTool(BaseTool):
    """Herramienta para búsqueda semántica en documentos"""

    def __init__(self):
        super().__init__(
            name="search_documents",
            description="Busca información relevante en la base de conocimientos de documentos"
        )

        # Definir el esquema de parámetros
        self.parameters_schema = {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Consulta para buscar información relevante en los documentos"
                }
            },
            "required": ["query"]
        }
    
    async def run(self, query: str) -> str:
        """
        Ejecutar búsqueda semántica
        
        Args:
            query: Consulta del usuario
            
        Returns:
            Contexto relevante encontrado en los documentos
        """
        try:
            # Generar embedding de la consulta
            query_embedding = await embedding_generator.generate_embedding(query)
            
            # Buscar chunks similares
            similar_chunks = await db_manager.search_similar_chunks(
                query_embedding=query_embedding,
                limit=5,
                similarity_threshold=0.7
            )
            
            if not similar_chunks:
                return "No se encontró información relevante en la base de conocimientos."
            
            # Formatear contexto
            context_parts = []
            for i, chunk in enumerate(similar_chunks, 1):
                context_parts.append(
                    f"[Documento {i}: {chunk['filename']}]\n"
                    f"Similitud: {chunk['similarity']:.2f}\n"
                    f"Contenido: {chunk['content']}\n"
                )
            
            context = "\n---\n".join(context_parts)
            
            logger.info(f"Encontrados {len(similar_chunks)} chunks relevantes para: {query}")
            return context
            
        except Exception as e:
            logger.error(f"Error en búsqueda RAG: {e}")
            return "Error al buscar información en la base de conocimientos."


def create_rag_agent(model_name: Optional[str] = None) -> Agent:
    """
    Crear un agente Q&A con capacidades RAG
    
    Args:
        model_name: Nombre del modelo a usar. Por defecto usa gemini-2.0-flash.
        
    Returns:
        Agent configurado para Q&A con RAG.
    """
    
    # Configurar modelo por defecto
    if model_name is None:
        model_name = "gemini-2.0-flash"
    
    # Determinar si usar LiteLlm o modelo directo
    if "/" in model_name:
        # Usar LiteLlm para modelos con formato provider/model
        model = LiteLlm(model=model_name)
    else:
        # Usar modelo directo para modelos de Gemini
        model = model_name
    
    # Crear herramienta de búsqueda RAG
    rag_tool = RAGSearchTool()
    
    # Crear agente de Q&A con RAG
    rag_agent = Agent(
        model=model,
        name="rag_qa_agent",
        description="Agente de preguntas y respuestas que utiliza RAG para responder únicamente basándose en documentos de la base de conocimientos.",
        instruction="""
        Eres un asistente de inteligencia artificial especializado en responder preguntas ÚNICAMENTE basándote en la información contenida en la base de conocimientos de documentos.

        REGLAS IMPORTANTES:
        1. SOLO puedes responder preguntas usando información que encuentres en los documentos de la base de conocimientos
        2. SIEMPRE debes usar la herramienta search_documents para buscar información relevante antes de responder
        3. Si no encuentras información relevante en los documentos, debes responder: "No tengo información sobre eso en mi base de conocimientos"
        4. NO inventes información ni uses conocimiento general fuera de los documentos
        5. Cita las fuentes cuando sea posible mencionando el nombre del documento

        PROCESO DE RESPUESTA:
        1. Usa search_documents para buscar información relevante sobre la consulta del usuario
        2. Si encuentras información relevante (similitud > 0.7), úsala para formular tu respuesta
        3. Si no encuentras información suficiente, indica claramente que no tienes esa información
        4. Estructura tu respuesta de manera clara y útil
        5. Menciona las fuentes de los documentos cuando sea apropiado

        FORMATO DE RESPUESTA:
        - Sé claro y directo
        - Usa la información encontrada para dar respuestas completas
        - Mantén un tono profesional y útil
        - Si citas múltiples documentos, organiza la información de manera coherente
        """,
        tools=[rag_tool],
        generate_content_config=types.GenerateContentConfig(
            temperature=0.3,  # Temperatura baja para respuestas más deterministas
            max_output_tokens=1024,
            safety_settings=[
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HARASSMENT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
            ]
        ),
    )
    
    return rag_agent


async def initialize_rag_system():
    """Inicializar el sistema RAG"""
    try:
        # Inicializar base de datos
        await db_manager.initialize()
        logger.info("Sistema RAG inicializado correctamente")
        return True
    except Exception as e:
        logger.error(f"Error inicializando sistema RAG: {e}")
        return False


# Crear instancia del agente RAG
root_agent = create_rag_agent()

# Variable para controlar si el sistema RAG está inicializado
_rag_initialized = False


async def ensure_rag_initialized():
    """Asegurar que el sistema RAG esté inicializado"""
    global _rag_initialized
    if not _rag_initialized:
        success = await initialize_rag_system()
        if success:
            _rag_initialized = True
        return success
    return True
