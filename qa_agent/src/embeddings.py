# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Generador de embeddings para RAG
"""

import os
import asyncio
from typing import List, Union, Optional
import logging
import numpy as np

# Importaciones para embeddings
try:
    import openai
    from sentence_transformers import SentenceTransformer
except ImportError as e:
    logging.warning(f"Algunas dependencias de embeddings no están disponibles: {e}")

logger = logging.getLogger(__name__)


class EmbeddingGenerator:
    """Generador de embeddings usando OpenAI o SentenceTransformers"""
    
    def __init__(self, provider: str = "openai", model_name: str = None):
        """
        Inicializar el generador de embeddings
        
        Args:
            provider: Proveedor de embeddings ("openai" o "sentence_transformers")
            model_name: Nombre del modelo específico
        """
        self.provider = provider.lower()
        self.model_name = model_name
        self.model = None
        self.embedding_dimension = None
        
        # Configurar según el proveedor
        if self.provider == "openai":
            self._setup_openai()
        elif self.provider == "sentence_transformers":
            self._setup_sentence_transformers()
        else:
            raise ValueError(f"Proveedor no soportado: {provider}")
    
    def _setup_openai(self):
        """Configurar OpenAI embeddings"""
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            logger.warning("OPENAI_API_KEY no encontrada, usando modelo local como fallback")
            self.provider = "sentence_transformers"
            self._setup_sentence_transformers()
            return
        
        try:
            openai.api_key = api_key
            self.model_name = self.model_name or "text-embedding-3-small"
            self.embedding_dimension = 1536  # Dimensión estándar de OpenAI embeddings
            logger.info(f"Configurado OpenAI embeddings con modelo: {self.model_name}")
        except Exception as e:
            logger.error(f"Error configurando OpenAI: {e}")
            logger.info("Fallback a sentence-transformers")
            self.provider = "sentence_transformers"
            self._setup_sentence_transformers()
    
    def _setup_sentence_transformers(self):
        """Configurar SentenceTransformers embeddings"""
        try:
            self.model_name = self.model_name or "all-MiniLM-L6-v2"
            self.model = SentenceTransformer(self.model_name)
            self.embedding_dimension = self.model.get_sentence_embedding_dimension()
            logger.info(f"Configurado SentenceTransformers con modelo: {self.model_name}")
        except Exception as e:
            logger.error(f"Error configurando SentenceTransformers: {e}")
            raise
    
    async def generate_embedding(self, text: str) -> List[float]:
        """
        Generar embedding para un texto
        
        Args:
            text: Texto para generar embedding
            
        Returns:
            Vector de embedding
        """
        if not text.strip():
            return [0.0] * self.embedding_dimension
        
        try:
            if self.provider == "openai":
                return await self._generate_openai_embedding(text)
            else:
                return await self._generate_sentence_transformer_embedding(text)
        except Exception as e:
            logger.error(f"Error generando embedding: {e}")
            # Retornar vector cero como fallback
            return [0.0] * self.embedding_dimension
    
    async def _generate_openai_embedding(self, text: str) -> List[float]:
        """Generar embedding usando OpenAI"""
        try:
            # Ejecutar en thread pool para no bloquear el event loop
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: openai.embeddings.create(
                    input=text,
                    model=self.model_name
                )
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error con OpenAI embeddings: {e}")
            raise
    
    async def _generate_sentence_transformer_embedding(self, text: str) -> List[float]:
        """Generar embedding usando SentenceTransformers"""
        try:
            # Ejecutar en thread pool para no bloquear el event loop
            loop = asyncio.get_event_loop()
            embedding = await loop.run_in_executor(
                None,
                lambda: self.model.encode(text, convert_to_numpy=True)
            )
            return embedding.tolist()
        except Exception as e:
            logger.error(f"Error con SentenceTransformers: {e}")
            raise
    
    async def generate_embeddings_batch(self, texts: List[str], batch_size: int = 10) -> List[List[float]]:
        """
        Generar embeddings para múltiples textos en lotes
        
        Args:
            texts: Lista de textos
            batch_size: Tamaño del lote
            
        Returns:
            Lista de vectores de embedding
        """
        embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            logger.info(f"Procesando lote {i//batch_size + 1}/{(len(texts)-1)//batch_size + 1}")
            
            if self.provider == "openai":
                # OpenAI puede procesar lotes
                batch_embeddings = await self._generate_openai_embeddings_batch(batch)
            else:
                # SentenceTransformers también puede procesar lotes
                batch_embeddings = await self._generate_sentence_transformer_embeddings_batch(batch)
            
            embeddings.extend(batch_embeddings)
            
            # Pequeña pausa para evitar rate limiting
            await asyncio.sleep(0.1)
        
        return embeddings
    
    async def _generate_openai_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """Generar embeddings en lote usando OpenAI"""
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: openai.embeddings.create(
                    input=texts,
                    model=self.model_name
                )
            )
            return [item.embedding for item in response.data]
        except Exception as e:
            logger.error(f"Error con lote OpenAI: {e}")
            # Fallback: generar uno por uno
            embeddings = []
            for text in texts:
                try:
                    embedding = await self.generate_embedding(text)
                    embeddings.append(embedding)
                except:
                    embeddings.append([0.0] * self.embedding_dimension)
            return embeddings
    
    async def _generate_sentence_transformer_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """Generar embeddings en lote usando SentenceTransformers"""
        try:
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                None,
                lambda: self.model.encode(texts, convert_to_numpy=True)
            )
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"Error con lote SentenceTransformers: {e}")
            # Fallback: generar uno por uno
            embeddings = []
            for text in texts:
                try:
                    embedding = await self.generate_embedding(text)
                    embeddings.append(embedding)
                except:
                    embeddings.append([0.0] * self.embedding_dimension)
            return embeddings
    
    def get_embedding_dimension(self) -> int:
        """Obtener la dimensión de los embeddings"""
        return self.embedding_dimension
    
    async def similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calcular similitud coseno entre dos embeddings
        
        Args:
            embedding1: Primer vector
            embedding2: Segundo vector
            
        Returns:
            Similitud coseno (0-1)
        """
        try:
            # Convertir a numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Calcular similitud coseno
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
        except Exception as e:
            logger.error(f"Error calculando similitud: {e}")
            return 0.0


# Función para crear instancia del generador de embeddings
def create_embedding_generator(provider: str = None, model_name: str = None) -> EmbeddingGenerator:
    """
    Crear instancia del generador de embeddings con configuración automática
    
    Args:
        provider: Proveedor preferido ("openai" o "sentence_transformers")
        model_name: Modelo específico
        
    Returns:
        Instancia configurada del generador
    """
    # Determinar proveedor automáticamente si no se especifica
    if provider is None:
        if os.getenv("OPENAI_API_KEY"):
            provider = "openai"
        else:
            provider = "sentence_transformers"
    
    return EmbeddingGenerator(provider=provider, model_name=model_name)


# Instancia global del generador de embeddings
embedding_generator = create_embedding_generator()
