#!/usr/bin/env python3
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Script principal para el agente Q&A con RAG
"""

import asyncio
import argparse
import sys
from pathlib import Path
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

from initialize_rag import RAGInitializer
from agent import root_agent, ensure_rag_initialized


async def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description="Agente Q&A con RAG")
    parser.add_argument("--init", action="store_true",
                       help="Inicializar el sistema RAG cargando documentos de Dropi")
    parser.add_argument("--force-reload", action="store_true",
                       help="Forzar recarga de todos los documentos de Dropi")
    parser.add_argument("--recreate-tables", action="store_true",
                       help="Recrear tablas con dimensiones correctas")
    parser.add_argument("--test-search", type=str,
                       help="Probar búsqueda con la consulta especificada sobre Dropi")
    parser.add_argument("--chat", action="store_true",
                       help="Iniciar chat interactivo sobre Dropi")
    parser.add_argument("--docs-folder", type=str, default="docs",
                       help="Carpeta con documentos de Dropi (por defecto: docs)")
    
    args = parser.parse_args()
    
    if args.init:
        # Inicializar sistema RAG para Dropi
        docs_folder = Path(args.docs_folder)
        if not docs_folder.is_absolute():
            docs_folder = Path(__file__).parent.parent / docs_folder

        initializer = RAGInitializer(docs_folder)
        success = await initializer.initialize_system(
            force_reload=args.force_reload,
            recreate_tables=args.recreate_tables
        )
        
        if not success:
            print("❌ Error durante la inicialización")
            sys.exit(1)
        
        # Probar búsqueda si se solicita
        if args.test_search:
            await initializer.test_search(args.test_search)
            
    elif args.chat:
        # Chat interactivo
        await interactive_chat()
        
    elif args.test_search:
        # Solo probar búsqueda
        await ensure_rag_initialized()
        docs_folder = Path(args.docs_folder)
        if not docs_folder.is_absolute():
            docs_folder = Path(__file__).parent.parent / docs_folder
            
        initializer = RAGInitializer(docs_folder)
        await initializer.test_search(args.test_search)
        
    else:
        parser.print_help()


async def interactive_chat():
    """Chat interactivo con el agente especializado en Dropi"""
    print("🤖 Agente Q&A de Dropi")
    print("Pregúntame cualquier cosa sobre Dropi!")
    print("Escribe 'salir' para terminar\n")
    
    # Asegurar que RAG esté inicializado
    await ensure_rag_initialized()
    
    while True:
        try:
            user_input = input("👤 Tú: ").strip()
            
            if user_input.lower() in ['salir', 'exit', 'quit']:
                print("👋 ¡Hasta luego!")
                break
                
            if not user_input:
                continue
                
            print("🤖 Agente: ", end="", flush=True)
            
            # Enviar mensaje al agente
            response = await root_agent.run(user_input)
            
            # Mostrar respuesta
            if hasattr(response, 'text'):
                print(response.text)
            else:
                print(str(response))
                
            print()  # Línea en blanco
            
        except KeyboardInterrupt:
            print("\n👋 ¡Hasta luego!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Programa interrumpido")
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        sys.exit(1)
