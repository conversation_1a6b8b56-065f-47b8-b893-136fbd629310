#!/usr/bin/env python3
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Script principal para ejecutar el agente de Q&A
"""

import asyncio
import os
import sys
from pathlib import Path

from dotenv import load_dotenv
from google.adk.runners import InMemoryRunner
from google.adk.sessions import Session
from google.genai import types

# Agregar el directorio src al path
sys.path.insert(0, str(Path(__file__).parent))

from agent import root_agent

# Cargar variables de entorno
load_dotenv()


class QAAgentRunner:
    """Runner para el agente de Q&A"""
    
    def __init__(self, app_name: str = "qa_agent_app"):
        self.app_name = app_name
        self.runner = InMemoryRunner(
            agent=root_agent,
            app_name=app_name,
        )
        self.current_session = None
    
    async def create_session(self, user_id: str = "default_user") -> Session:
        """Crear una nueva sesión para el usuario"""
        session = await self.runner.session_service.create_session(
            app_name=self.app_name, 
            user_id=user_id
        )
        self.current_session = session
        return session
    
    async def ask_question(self, question: str, user_id: str = "default_user") -> str:
        """
        Hacer una pregunta al agente y obtener la respuesta
        
        Args:
            question: La pregunta a hacer
            user_id: ID del usuario
            
        Returns:
            La respuesta del agente
        """
        if not self.current_session:
            await self.create_session(user_id)
        
        # Crear contenido de la pregunta
        content = types.Content(
            role='user', 
            parts=[types.Part.from_text(text=question)]
        )
        
        # Ejecutar el agente y recopilar la respuesta
        response_parts = []
        async for event in self.runner.run_async(
            user_id=user_id,
            session_id=self.current_session.id,
            new_message=content,
        ):
            if event.content.parts and event.content.parts[0].text:
                response_parts.append(event.content.parts[0].text)
        
        return "".join(response_parts)
    
    async def interactive_chat(self):
        """Modo de chat interactivo"""
        print("🤖 Agente Q&A iniciado. Escribe 'quit' para salir.\n")
        
        user_id = "interactive_user"
        await self.create_session(user_id)
        
        while True:
            try:
                question = input("👤 Pregunta: ").strip()
                
                if question.lower() in ['quit', 'exit', 'salir']:
                    print("👋 ¡Hasta luego!")
                    break
                
                if not question:
                    continue
                
                print("🤖 Pensando...")
                response = await self.ask_question(question, user_id)
                print(f"🤖 Respuesta: {response}\n")
                
            except KeyboardInterrupt:
                print("\n👋 ¡Hasta luego!")
                break
            except Exception as e:
                print(f"❌ Error: {e}\n")


async def main():
    """Función principal"""
    print("=== Agente Q&A con Google ADK ===\n")
    
    # Verificar configuración
    print("Verificando configuración...")
    
    # Verificar variables de entorno necesarias
    required_env_vars = []
    
    # Para Gemini, verificar si hay API key configurada
    if not os.getenv("GOOGLE_API_KEY") and not os.getenv("GOOGLE_APPLICATION_CREDENTIALS"):
        print("⚠️  Nota: No se encontró GOOGLE_API_KEY ni GOOGLE_APPLICATION_CREDENTIALS")
        print("   El agente intentará usar la configuración por defecto.")
    else:
        print("✅ Configuración de autenticación encontrada")
    
    print("✅ Configuración verificada\n")
    
    # Crear y ejecutar el runner
    qa_runner = QAAgentRunner()
    
    # Verificar argumentos de línea de comandos
    if len(sys.argv) > 1:
        # Modo de pregunta única
        question = " ".join(sys.argv[1:])
        print(f"Pregunta: {question}")
        
        try:
            response = await qa_runner.ask_question(question)
            print(f"Respuesta: {response}")
        except Exception as e:
            print(f"Error: {e}")
            sys.exit(1)
    else:
        # Modo interactivo
        await qa_runner.interactive_chat()


if __name__ == "__main__":
    asyncio.run(main())
