#!/usr/bin/env python3
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Script de inicialización para cargar documentos en el sistema RAG
"""

import asyncio
import os
import sys
from pathlib import Path
import logging
from dotenv import load_dotenv

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Cargar variables de entorno
load_dotenv()

# Agregar el directorio actual al path
sys.path.insert(0, str(Path(__file__).parent))

from database import db_manager
from embeddings import embedding_generator
from document_processor import document_processor


class RAGInitializer:
    """Inicializador del sistema RAG"""
    
    def __init__(self, docs_folder: Path = None):
        """
        Inicializar el inicializador RAG
        
        Args:
            docs_folder: Carpeta con documentos a procesar
        """
        self.docs_folder = docs_folder or Path(__file__).parent.parent / "docs"
        
    async def initialize_system(self, force_reload: bool = False, recreate_tables: bool = False):
        """
        Inicializar completamente el sistema RAG

        Args:
            force_reload: Si True, elimina y recarga todos los documentos
            recreate_tables: Si True, recrea las tablas con dimensiones correctas
        """
        logger.info("=== Inicializando Sistema RAG para Dropi ===")

        try:
            # 1. Inicializar base de datos
            logger.info("1. Inicializando base de datos...")
            await db_manager.initialize()

            # 2. Recrear tablas si es necesario
            if recreate_tables:
                logger.info("Recreando tablas con dimensiones correctas...")
                await db_manager.recreate_tables()

            # 3. Verificar si ya hay documentos cargados
            doc_count = await db_manager.get_document_count()
            chunk_count = await db_manager.get_chunk_count()

            logger.info(f"Estado actual: {doc_count} documentos, {chunk_count} chunks")

            if force_reload and doc_count > 0:
                logger.info("Forzando recarga: eliminando documentos existentes...")
                await db_manager.clear_all_documents()
                doc_count = 0
            
            # 4. Cargar documentos si es necesario
            if doc_count == 0:
                await self.load_documents()
            else:
                logger.info("Los documentos ya están cargados. Use --force-reload para recargar.")
            
            # 5. Verificar estado final
            final_doc_count = await db_manager.get_document_count()
            final_chunk_count = await db_manager.get_chunk_count()

            logger.info(f"=== Inicialización de Dropi RAG Completada ===")
            logger.info(f"Documentos de Dropi: {final_doc_count}")
            logger.info(f"Chunks de Dropi: {final_chunk_count}")
            logger.info(f"Dimensión embeddings: {embedding_generator.get_embedding_dimension()}")
            logger.info("🎉 Sistema RAG de Dropi listo para responder preguntas sobre Dropi!")
            
            return True
            
        except Exception as e:
            logger.error(f"Error durante la inicialización: {e}")
            return False
    
    async def load_documents(self):
        """Cargar y procesar documentos desde la carpeta docs"""
        logger.info("2. Cargando documentos...")
        
        if not self.docs_folder.exists():
            logger.warning(f"La carpeta {self.docs_folder} no existe. Creándola...")
            self.docs_folder.mkdir(parents=True, exist_ok=True)
            logger.info("Carpeta 'docs' creada. Agregue documentos y ejecute nuevamente.")
            return
        
        # Verificar si hay documentos
        doc_files = list(self.docs_folder.glob("*"))
        doc_files = [f for f in doc_files if f.is_file() and f.suffix.lower() in {'.txt', '.pdf', '.docx', '.doc', '.md'}]
        
        if not doc_files:
            logger.warning("No se encontraron documentos en la carpeta 'docs'.")
            logger.info("Formatos soportados: .txt, .pdf, .docx, .doc, .md")
            return
        
        logger.info(f"Encontrados {len(doc_files)} documentos para procesar")
        
        # Procesar documentos
        processed_docs = await document_processor.process_documents_folder(self.docs_folder)
        
        if not processed_docs:
            logger.warning("No se pudieron procesar documentos")
            return
        
        logger.info("3. Generando embeddings y almacenando en base de datos...")
        
        total_chunks = 0
        for doc in processed_docs:
            try:
                # Verificar si el documento ya existe
                if await db_manager.document_exists(doc['filename']):
                    logger.info(f"Documento {doc['filename']} ya existe, saltando...")
                    continue
                
                logger.info(f"Procesando: {doc['filename']} ({len(doc['chunks'])} chunks)")
                
                # Insertar documento
                doc_id = await db_manager.insert_document(
                    filename=doc['filename'],
                    content=doc['content'],
                    metadata=doc['metadata']
                )
                
                # Procesar chunks en lotes
                chunk_contents = [chunk['content'] for chunk in doc['chunks']]
                
                if chunk_contents:
                    # Generar embeddings en lotes
                    embeddings = await embedding_generator.generate_embeddings_batch(
                        chunk_contents, 
                        batch_size=10
                    )
                    
                    # Insertar chunks con embeddings
                    for i, (chunk, embedding) in enumerate(zip(doc['chunks'], embeddings)):
                        await db_manager.insert_chunk(
                            document_id=doc_id,
                            chunk_index=chunk['chunk_index'],
                            content=chunk['content'],
                            embedding=embedding,
                            metadata=chunk['metadata']
                        )
                    
                    total_chunks += len(doc['chunks'])
                    logger.info(f"✅ {doc['filename']}: {len(doc['chunks'])} chunks procesados")
                
            except Exception as e:
                logger.error(f"Error procesando {doc['filename']}: {e}")
                continue
        
        logger.info(f"✅ Procesamiento completado: {total_chunks} chunks totales")
    
    async def test_search(self, query: str = "¿Qué información hay disponible?"):
        """
        Probar la funcionalidad de búsqueda
        
        Args:
            query: Consulta de prueba
        """
        logger.info(f"=== Probando búsqueda: '{query}' ===")
        
        try:
            # Generar embedding de la consulta
            query_embedding = await embedding_generator.generate_embedding(query)
            
            # Buscar chunks similares
            results = await db_manager.search_similar_chunks(
                query_embedding=query_embedding,
                limit=3,
                similarity_threshold=0.4
            )
            
            if results:
                logger.info(f"Encontrados {len(results)} resultados:")
                for i, result in enumerate(results, 1):
                    logger.info(f"\n{i}. Documento: {result['filename']}")
                    logger.info(f"   Similitud: {result['similarity']:.3f}")
                    logger.info(f"   Contenido: {result['content'][:200]}...")
            else:
                logger.info("No se encontraron resultados")
                
        except Exception as e:
            logger.error(f"Error en búsqueda de prueba: {e}")


async def main():
    """Función principal"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Inicializar sistema RAG")
    parser.add_argument("--force-reload", action="store_true", 
                       help="Forzar recarga de todos los documentos")
    parser.add_argument("--test-search", type=str, 
                       help="Probar búsqueda con la consulta especificada")
    parser.add_argument("--docs-folder", type=str,
                       help="Carpeta con documentos (por defecto: ./docs)")
    
    args = parser.parse_args()
    
    # Configurar carpeta de documentos
    docs_folder = Path(args.docs_folder) if args.docs_folder else None
    
    # Crear inicializador
    initializer = RAGInitializer(docs_folder)
    
    try:
        # Inicializar sistema
        success = await initializer.initialize_system(force_reload=args.force_reload)
        
        if not success:
            logger.error("Falló la inicialización del sistema")
            sys.exit(1)
        
        # Probar búsqueda si se solicita
        if args.test_search:
            await initializer.test_search(args.test_search)
        
        logger.info("🎉 Sistema RAG listo para usar!")
        
    except KeyboardInterrupt:
        logger.info("Inicialización cancelada por el usuario")
    except Exception as e:
        logger.error(f"Error inesperado: {e}")
        sys.exit(1)
    finally:
        # Cerrar conexiones
        await db_manager.close()


if __name__ == "__main__":
    asyncio.run(main())
