# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Módulo de base de datos para RAG con PostgreSQL y pgvector
"""

import asyncio
import os
from typing import List, Dict, Any, Optional, Tuple
import asyncpg
import numpy as np
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Gestor de base de datos para almacenamiento de documentos y embeddings"""
    
    def __init__(self, database_url: str = None):
        """
        Inicializar el gestor de base de datos

        Args:
            database_url: URL de conexión a PostgreSQL
        """
        self.database_url = database_url or os.getenv("DATABASE_URL")
        if not self.database_url:
            raise ValueError("DATABASE_URL no está configurada")

        # Convertir URL de SQLAlchemy a asyncpg si es necesario
        if self.database_url.startswith("postgresql+asyncpg://"):
            self.database_url = self.database_url.replace("postgresql+asyncpg://", "postgresql://")

        self.pool = None
        self.schema_name = "agente_v5"
    
    async def initialize(self):
        """Inicializar el pool de conexiones y crear el esquema si no existe"""
        try:
            # Crear pool de conexiones
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=2,
                max_size=10,
                command_timeout=60
            )
            
            # Crear esquema y tablas
            await self.create_schema()
            await self.create_tables()
            
            logger.info("Base de datos inicializada correctamente")
            
        except Exception as e:
            logger.error(f"Error inicializando base de datos: {e}")
            raise
    
    async def close(self):
        """Cerrar el pool de conexiones"""
        if self.pool:
            await self.pool.close()
    
    async def create_schema(self):
        """Crear el esquema agente_v5 si no existe"""
        async with self.pool.acquire() as conn:
            # Habilitar extensión pgvector
            await conn.execute("CREATE EXTENSION IF NOT EXISTS vector;")
            
            # Crear esquema
            await conn.execute(f"CREATE SCHEMA IF NOT EXISTS {self.schema_name};")
            
            logger.info(f"Esquema {self.schema_name} creado/verificado")
    
    async def create_tables(self):
        """Crear las tablas necesarias para RAG"""
        async with self.pool.acquire() as conn:
            # Tabla para documentos
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.schema_name}.documents (
                    id SERIAL PRIMARY KEY,
                    filename VARCHAR(255) NOT NULL,
                    content TEXT NOT NULL,
                    metadata JSONB DEFAULT '{{}}',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Tabla para chunks de documentos con embeddings
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.schema_name}.document_chunks (
                    id SERIAL PRIMARY KEY,
                    document_id INTEGER REFERENCES {self.schema_name}.documents(id) ON DELETE CASCADE,
                    chunk_index INTEGER NOT NULL,
                    content TEXT NOT NULL,
                    embedding vector(384),  -- Dimensión para SentenceTransformers
                    metadata JSONB DEFAULT '{{}}',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Índices para optimizar búsquedas
            await conn.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_document_chunks_embedding 
                ON {self.schema_name}.document_chunks 
                USING ivfflat (embedding vector_cosine_ops) 
                WITH (lists = 100);
            """)
            
            await conn.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_documents_filename 
                ON {self.schema_name}.documents (filename);
            """)
            
            await conn.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_document_chunks_document_id 
                ON {self.schema_name}.document_chunks (document_id);
            """)
            
            logger.info("Tablas creadas/verificadas correctamente")
    
    async def insert_document(self, filename: str, content: str, metadata: Dict[str, Any] = None) -> int:
        """
        Insertar un documento en la base de datos

        Args:
            filename: Nombre del archivo
            content: Contenido del documento
            metadata: Metadatos adicionales

        Returns:
            ID del documento insertado
        """
        import json
        metadata = metadata or {}

        async with self.pool.acquire() as conn:
            result = await conn.fetchrow(f"""
                INSERT INTO {self.schema_name}.documents (filename, content, metadata)
                VALUES ($1, $2, $3)
                RETURNING id;
            """, filename, content, json.dumps(metadata))

            return result['id']
    
    async def insert_chunk(self, document_id: int, chunk_index: int, content: str,
                          embedding: List[float], metadata: Dict[str, Any] = None) -> int:
        """
        Insertar un chunk con su embedding

        Args:
            document_id: ID del documento padre
            chunk_index: Índice del chunk en el documento
            content: Contenido del chunk
            embedding: Vector de embedding
            metadata: Metadatos adicionales

        Returns:
            ID del chunk insertado
        """
        import json
        metadata = metadata or {}

        async with self.pool.acquire() as conn:
            # Convertir embedding a string para pgvector
            embedding_str = str(embedding)

            result = await conn.fetchrow(f"""
                INSERT INTO {self.schema_name}.document_chunks
                (document_id, chunk_index, content, embedding, metadata)
                VALUES ($1, $2, $3, $4::vector, $5)
                RETURNING id;
            """, document_id, chunk_index, content, embedding_str, json.dumps(metadata))

            return result['id']
    
    async def search_similar_chunks(self, query_embedding: List[float], 
                                   limit: int = 5, similarity_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        Buscar chunks similares usando búsqueda vectorial
        
        Args:
            query_embedding: Vector de embedding de la consulta
            limit: Número máximo de resultados
            similarity_threshold: Umbral mínimo de similitud
            
        Returns:
            Lista de chunks similares con metadatos
        """
        async with self.pool.acquire() as conn:
            # Convertir query_embedding a string para pgvector
            query_embedding_str = str(query_embedding)

            results = await conn.fetch(f"""
                SELECT
                    dc.id,
                    dc.content,
                    dc.chunk_index,
                    dc.metadata as chunk_metadata,
                    d.filename,
                    d.metadata as document_metadata,
                    1 - (dc.embedding <=> $1::vector) as similarity
                FROM {self.schema_name}.document_chunks dc
                JOIN {self.schema_name}.documents d ON dc.document_id = d.id
                WHERE 1 - (dc.embedding <=> $1::vector) > $2
                ORDER BY dc.embedding <=> $1::vector
                LIMIT $3;
            """, query_embedding_str, similarity_threshold, limit)
            
            return [dict(row) for row in results]
    
    async def get_document_count(self) -> int:
        """Obtener el número total de documentos"""
        async with self.pool.acquire() as conn:
            result = await conn.fetchrow(f"SELECT COUNT(*) as count FROM {self.schema_name}.documents;")
            return result['count']
    
    async def get_chunk_count(self) -> int:
        """Obtener el número total de chunks"""
        async with self.pool.acquire() as conn:
            result = await conn.fetchrow(f"SELECT COUNT(*) as count FROM {self.schema_name}.document_chunks;")
            return result['count']
    
    async def document_exists(self, filename: str) -> bool:
        """Verificar si un documento ya existe"""
        async with self.pool.acquire() as conn:
            result = await conn.fetchrow(f"""
                SELECT id FROM {self.schema_name}.documents WHERE filename = $1;
            """, filename)
            return result is not None
    
    async def clear_all_documents(self):
        """Limpiar todos los documentos y chunks (para reinicialización)"""
        async with self.pool.acquire() as conn:
            await conn.execute(f"DELETE FROM {self.schema_name}.document_chunks;")
            await conn.execute(f"DELETE FROM {self.schema_name}.documents;")
            logger.info("Todos los documentos han sido eliminados")


# Instancia global del gestor de base de datos
db_manager = DatabaseManager()
