# Agente Q&A con RAG

Un agente de preguntas y respuestas con capacidades RAG (Retrieval-Augmented Generation) construido con Google ADK (Agent Development Kit).

## Características

- 🤖 Agente conversacional inteligente con RAG
- 📚 Búsqueda semántica en documentos
- 🗄️ Base de datos vectorial con PostgreSQL + pgvector
- 🌐 Interfaz web integrada con ADK
- ⚡ Respuestas basadas únicamente en documentos cargados
- 🔧 Fácil configuración y personalización
- 🎯 Soporte para múltiples modelos de LLM y embeddings

## Requisitos

- Python 3.11+
- Conda (recomendado)
- PostgreSQL con extensión pgvector
- API key de un proveedor de LLM (Gemini, OpenAI, etc.)

## Instalación

### 1. Crear entorno conda

```bash
conda create -n qa_agent python=3.11
conda activate qa_agent
```

### 2. Instalar dependencias

```bash
pip install -r requirements.txt
```

### 3. Configurar PostgreSQL

Asegúrate de tener PostgreSQL con pgvector instalado y configurado. La base de datos debe estar accesible con las credenciales en el archivo `.env`.

### 4. Configurar variables de entorno

Copia el archivo `.env.example` a `.env` y configura tus credenciales:

```bash
cp .env.example .env
```

Edita el archivo `.env` con tus credenciales:

```env
# Configuración del modelo
MODEL_NAME=gemini-2.0-flash

# API Keys (configura según el modelo que uses)
GOOGLE_API_KEY=tu_api_key_aqui
OPENAI_API_KEY=tu_api_key_aqui
ANTHROPIC_API_KEY=tu_api_key_aqui

# Configuración de base de datos
DATABASE_URL=postgresql+asyncpg://usuario:password@host:puerto/database
DB_HOST=localhost
DB_NAME=qa_agent
DB_USER=postgres
DB_PASSWORD=tu_password
DB_PORT=5432
```

## Uso

### 1. Inicializar el Sistema RAG

Antes de usar el agente, debes cargar documentos en la base de datos:

```bash
# Coloca tus documentos en la carpeta 'docs/'
# Formatos soportados: .txt, .pdf, .docx, .md

# Inicializar el sistema RAG
python src/main.py --init

# Forzar recarga de documentos
python src/main.py --init --force-reload
```

### 2. Interfaz Web

Para usar la interfaz web integrada con ADK:

```bash
# Desde el directorio raíz del proyecto
adk web
```

Luego abre tu navegador en `http://localhost:8000` y selecciona el agente "qa_agent_web".

### 3. Chat Interactivo

```bash
# Chat en línea de comandos
python src/main.py --chat
```

### 4. Probar Búsqueda

```bash
# Probar la búsqueda semántica
python src/main.py --test-search "¿Qué es RAG?"
```

### 5. Uso Programático

```python
import asyncio
from src.agent import root_agent, ensure_rag_initialized

async def main():
    # Asegurar que RAG esté inicializado
    await ensure_rag_initialized()

    # Enviar mensaje al agente
    response = await root_agent.send_message("¿Qué información tienes sobre RAG?")
    print(response.text)

asyncio.run(main())
```

## Configuración

### Modelos de LLM Soportados

- **Gemini**: `gemini-2.0-flash`, `gemini-1.5-pro`
- **OpenAI**: `gpt-4`, `gpt-3.5-turbo`
- **Anthropic**: `claude-3-sonnet`, `claude-3-haiku`
- **Otros**: Cualquier modelo compatible con LiteLLM

### Modelos de Embeddings

- **OpenAI**: `text-embedding-3-small`, `text-embedding-ada-002`
- **SentenceTransformers**: `all-MiniLM-L6-v2` (local, sin API key)

### Documentos Soportados

- **Texto plano**: `.txt`
- **PDF**: `.pdf`
- **Word**: `.docx`, `.doc`
- **Markdown**: `.md`

### Personalización

Puedes personalizar el comportamiento editando los archivos en `src/`:

- `agent.py`: Configuración del agente principal
- `database.py`: Configuración de base de datos
- `embeddings.py`: Configuración de embeddings
- `document_processor.py`: Procesamiento de documentos

## Estructura del Proyecto

```
qa_agent/
├── qa_agent_web/          # Agente para interfaz web ADK
│   ├── __init__.py
│   └── agent.py
├── src/                   # Implementación core del agente
│   ├── __init__.py
│   ├── agent.py          # Agente principal con RAG
│   ├── database.py       # Gestión de PostgreSQL + pgvector
│   ├── embeddings.py     # Generación de embeddings
│   ├── document_processor.py  # Procesamiento de documentos
│   ├── initialize_rag.py # Script de inicialización
│   └── main.py          # Script principal
├── docs/                 # Documentos para RAG
│   └── ejemplo_conocimiento.md
├── .env                  # Configuración (no incluido en git)
├── .env.example         # Plantilla de configuración
├── requirements.txt     # Dependencias
└── README.md           # Este archivo
```

## Desarrollo

### Agregar Nuevas Funcionalidades

1. Edita `src/agent.py` para modificar el agente principal
2. Agrega nuevas herramientas en el directorio `src/`
3. Actualiza `requirements.txt` si agregas nuevas dependencias

### Testing

```bash
# Ejecutar tests
pytest

# Con cobertura
pytest --cov=src
```

## Solución de Problemas

### Error de API Key

Si ves errores relacionados con API keys:

1. Verifica que el archivo `.env` existe y tiene las keys correctas
2. Asegúrate de que la key corresponde al modelo configurado
3. Verifica que la key tiene los permisos necesarios

### Error de Base de Datos

Si hay problemas con PostgreSQL:

1. Verifica que PostgreSQL esté ejecutándose
2. Asegúrate de que la extensión pgvector esté instalada
3. Verifica las credenciales en el archivo `.env`

### Error de Dependencias

Si hay problemas con las dependencias:

```bash
# Reinstalar dependencias
pip install --force-reinstall -r requirements.txt

# O crear un nuevo entorno
conda deactivate
conda remove -n qa_agent --all
conda create -n qa_agent python=3.11
conda activate qa_agent
pip install -r requirements.txt
```

## Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un Pull Request

## Licencia

Este proyecto está licenciado bajo la Licencia Apache 2.0. Ver el archivo `LICENSE` para más detalles.

## Soporte

Si tienes problemas o preguntas:

1. Revisa la documentación de [Google ADK](https://github.com/google/adk)
2. Busca en los issues existentes
3. Crea un nuevo issue con detalles del problema
3. **Configuración**: Actualiza `.env.example` con nuevas variables

### Ejemplo: Agregar una Herramienta

```python
def search_web(query: str) -> str:
    """Buscar información en la web"""
    # Implementar búsqueda
    return "Resultados de búsqueda..."

# En agent.py
qa_agent = Agent(
    # ... configuración existente ...
    tools=[search_web],  # Agregar herramienta
)
```

## 🚀 Próximos Pasos

Este PoC está diseñado para ser iterado rápidamente. Algunas mejoras planificadas:

1. **Integración con Base de Datos**: Conectar a bases de datos para consultas específicas
2. **Búsqueda Semántica**: Implementar RAG (Retrieval-Augmented Generation)
3. **Memoria Persistente**: Recordar conversaciones anteriores
4. **API REST**: Exponer el agente como servicio web
5. **Interfaz Web**: Frontend para interactuar con el agente

## 🐛 Troubleshooting

### Error de Autenticación
```
Error: No hay configuración de API disponible
```
**Solución**: Configura `GOOGLE_API_KEY` en tu archivo `.env`

### Error de Modelo
```
Error: Model not found
```
**Solución**: Verifica que el modelo especificado esté disponible y correctamente configurado

### Error de Dependencias
```
ModuleNotFoundError: No module named 'google.adk'
```
**Solución**: Instala las dependencias con `pip install -r requirements.txt`

## 📄 Licencia

Copyright 2025 Google LLC. Licenciado bajo Apache License 2.0.

## 🤝 Contribuir

Este es un PoC en desarrollo activo. Las contribuciones son bienvenidas:

1. Fork el proyecto
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

---

**¿Preguntas?** Abre un issue o contacta al equipo de desarrollo.
