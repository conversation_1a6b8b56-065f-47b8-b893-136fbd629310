# Agente Q&A Simple con Google ADK

Un agente simple de preguntas y respuestas (Q&A) construido con Google Agent Development Kit (ADK). Este es un Proof of Concept (PoC) diseñado para ser simple, rápido de implementar y fácil de iterar.

## 🚀 Características

- **Agente Q&A simple**: Responde preguntas de manera clara y precisa
- **Múltiples modelos**: Soporte para Gemini, OpenAI, Claude y otros a través de LiteLLM
- **Fácil configuración**: Setup mínimo con variables de entorno
- **Modo interactivo**: Chat en tiempo real desde la línea de comandos
- **Modo de pregunta única**: Ejecutar una sola pregunta y obtener respuesta
- **Extensible**: Base sólida para agregar funcionalidades como RAG y búsqueda semántica

## 📋 Requisitos

- Python 3.9+
- Google API Key o credenciales de Google Cloud
- Dependencias listadas en `requirements.txt`

## 🛠️ Instalación

1. **Clonar o copiar el proyecto**:
   ```bash
   cd qa_agent
   ```

2. **Instalar dependencias**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configurar variables de entorno**:
   ```bash
   cp .env.example .env
   # Editar .env con tu configuración
   ```

4. **Configurar autenticación** (elige una opción):

   **Opción A: Google API Key (recomendado para desarrollo)**
   ```bash
   # En tu .env
   GOOGLE_API_KEY=tu_api_key_aqui
   ```
   Obtén tu API key en: https://aistudio.google.com/app/apikey

   **Opción B: Google Cloud Credentials**
   ```bash
   # En tu .env
   GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json
   ```

## 🎯 Uso

### Modo Interactivo

Ejecuta el agente en modo chat interactivo:

```bash
cd src
python main.py
```

Ejemplo de uso:
```
🤖 Agente Q&A iniciado. Escribe 'quit' para salir.

👤 Pregunta: ¿Qué es la inteligencia artificial?
🤖 Pensando...
🤖 Respuesta: La inteligencia artificial (IA) es una rama de la informática que se enfoca en crear sistemas capaces de realizar tareas que normalmente requieren inteligencia humana...

👤 Pregunta: quit
👋 ¡Hasta luego!
```

### Modo de Pregunta Única

Ejecuta una sola pregunta desde la línea de comandos:

```bash
cd src
python main.py "¿Cuál es la capital de Francia?"
```

### Ejecutar Tests

```bash
cd tests
python test_qa_agent.py
```

O usando pytest:
```bash
pytest tests/
```

## ⚙️ Configuración

### Variables de Entorno Principales

| Variable | Descripción | Valor por Defecto |
|----------|-------------|-------------------|
| `GOOGLE_API_KEY` | API Key de Google para Gemini | - |
| `DEFAULT_MODEL` | Modelo a usar | `gemini-2.0-flash` |
| `TEMPERATURE` | Creatividad del modelo (0.0-1.0) | `0.7` |
| `MAX_OUTPUT_TOKENS` | Máximo tokens en respuesta | `1024` |

### Modelos Soportados

- **Gemini** (recomendado): `gemini-2.0-flash`, `gemini-1.5-pro`
- **OpenAI**: `openai/gpt-4o`, `openai/gpt-3.5-turbo`
- **Claude**: `anthropic/claude-3-sonnet-20240229`
- **Vertex AI**: `vertex_ai/gemini-2.0-flash`

## 📁 Estructura del Proyecto

```
qa_agent/
├── src/
│   ├── agent.py          # Definición del agente Q&A
│   └── main.py           # Script principal de ejecución
├── tests/
│   └── test_qa_agent.py  # Tests del agente
├── .env.example          # Plantilla de configuración
├── requirements.txt      # Dependencias
└── README.md            # Esta documentación
```

## 🔧 Desarrollo

### Agregar Nuevas Funcionalidades

1. **Herramientas (Tools)**: Edita `agent.py` y agrega tools al agente
2. **Modelos**: Modifica `create_qa_agent()` para soportar nuevos modelos
3. **Configuración**: Actualiza `.env.example` con nuevas variables

### Ejemplo: Agregar una Herramienta

```python
def search_web(query: str) -> str:
    """Buscar información en la web"""
    # Implementar búsqueda
    return "Resultados de búsqueda..."

# En agent.py
qa_agent = Agent(
    # ... configuración existente ...
    tools=[search_web],  # Agregar herramienta
)
```

## 🚀 Próximos Pasos

Este PoC está diseñado para ser iterado rápidamente. Algunas mejoras planificadas:

1. **Integración con Base de Datos**: Conectar a bases de datos para consultas específicas
2. **Búsqueda Semántica**: Implementar RAG (Retrieval-Augmented Generation)
3. **Memoria Persistente**: Recordar conversaciones anteriores
4. **API REST**: Exponer el agente como servicio web
5. **Interfaz Web**: Frontend para interactuar con el agente

## 🐛 Troubleshooting

### Error de Autenticación
```
Error: No hay configuración de API disponible
```
**Solución**: Configura `GOOGLE_API_KEY` en tu archivo `.env`

### Error de Modelo
```
Error: Model not found
```
**Solución**: Verifica que el modelo especificado esté disponible y correctamente configurado

### Error de Dependencias
```
ModuleNotFoundError: No module named 'google.adk'
```
**Solución**: Instala las dependencias con `pip install -r requirements.txt`

## 📄 Licencia

Copyright 2025 Google LLC. Licenciado bajo Apache License 2.0.

## 🤝 Contribuir

Este es un PoC en desarrollo activo. Las contribuciones son bienvenidas:

1. Fork el proyecto
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

---

**¿Preguntas?** Abre un issue o contacta al equipo de desarrollo.
