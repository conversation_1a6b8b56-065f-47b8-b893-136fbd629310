# Dependencias para el Agente Q&A con RAG

# Google ADK - Agent Development Kit
google-adk>=1.0.0

# Manejo de variables de entorno
python-dotenv>=1.0.0

# Para usar LiteLLM con múltiples proveedores
litellm>=1.71.2

# PostgreSQL y pgvector para RAG
asyncpg>=0.29.0
psycopg2-binary>=2.9.9
pgvector>=0.2.4

# Embeddings y procesamiento de texto
openai>=1.51.0
sentence-transformers>=2.2.2
tiktoken>=0.7.0

# Procesamiento de documentos
langchain>=0.1.0
langchain-community>=0.0.13
langchain-openai>=0.0.5
pypdf>=4.0.0
python-docx>=1.1.0
unstructured>=0.12.0

# Utilidades adicionales
numpy>=1.24.0
pandas>=2.0.0

# Para desarrollo y testing
pytest>=8.0.0
pytest-asyncio>=0.25.0
