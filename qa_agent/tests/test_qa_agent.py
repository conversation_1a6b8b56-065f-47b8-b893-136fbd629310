#!/usr/bin/env python3
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Tests para el agente Q&A
"""

import asyncio
import os
import sys
from pathlib import Path

import pytest
from dotenv import load_dotenv

# Agregar el directorio src al path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent import create_qa_agent, root_agent
from main import QAAgentRunner

# Cargar variables de entorno
load_dotenv()


class TestQAAgent:
    """Tests para el agente Q&A"""
    
    def test_agent_creation(self):
        """Test que el agente se puede crear correctamente"""
        agent = create_qa_agent()
        assert agent is not None
        assert agent.name == "qa_agent"
        assert "preguntas y respuestas" in agent.description
    
    def test_agent_with_custom_model(self):
        """Test que el agente se puede crear con un modelo personalizado"""
        agent = create_qa_agent("gemini-1.5-pro")
        assert agent is not None
        assert agent.model == "gemini-1.5-pro"
    
    def test_agent_with_litellm_model(self):
        """Test que el agente se puede crear con un modelo de LiteLLM"""
        agent = create_qa_agent("openai/gpt-4o")
        assert agent is not None
        # El modelo debería ser una instancia de LiteLlm
        from google.adk.models.lite_llm import LiteLlm
        assert isinstance(agent.model, LiteLlm)
    
    def test_root_agent_exists(self):
        """Test que el agente root existe y está configurado"""
        assert root_agent is not None
        assert root_agent.name == "qa_agent"


class TestQAAgentRunner:
    """Tests para el runner del agente Q&A"""
    
    @pytest.fixture
    def runner(self):
        """Fixture para crear un runner de prueba"""
        return QAAgentRunner("test_app")
    
    @pytest.mark.asyncio
    async def test_runner_creation(self, runner):
        """Test que el runner se puede crear correctamente"""
        assert runner is not None
        assert runner.app_name == "test_app"
        assert runner.runner is not None
    
    @pytest.mark.asyncio
    async def test_session_creation(self, runner):
        """Test que se puede crear una sesión"""
        session = await runner.create_session("test_user")
        assert session is not None
        assert runner.current_session == session
    
    @pytest.mark.asyncio
    async def test_simple_question(self, runner):
        """Test una pregunta simple al agente"""
        # Solo ejecutar si hay configuración de API
        if not os.getenv("GOOGLE_API_KEY") and not os.getenv("GOOGLE_APPLICATION_CREDENTIALS"):
            pytest.skip("No hay configuración de API disponible")
        
        try:
            response = await runner.ask_question("¿Qué es 2 + 2?", "test_user")
            assert response is not None
            assert len(response) > 0
            print(f"Respuesta del agente: {response}")
        except Exception as e:
            pytest.skip(f"Error de API o configuración: {e}")


async def test_basic_functionality():
    """
    Test básico de funcionalidad del agente
    """
    print("=== Test Básico del Agente Q&A ===\n")
    
    # Verificar configuración
    print("1. Verificando configuración...")
    
    if not os.getenv("GOOGLE_API_KEY") and not os.getenv("GOOGLE_APPLICATION_CREDENTIALS"):
        print("⚠️  No hay configuración de API. Saltando tests de integración.")
        return False
    else:
        print("✅ Configuración de API encontrada")
    
    print("\n2. Creando agente...")
    
    try:
        # Crear agente
        agent = create_qa_agent()
        print("✅ Agente creado correctamente")
        
        print("\n3. Creando runner...")
        
        # Crear runner
        runner = QAAgentRunner("test_app")
        print("✅ Runner creado correctamente")
        
        print("\n4. Probando preguntas...")
        
        # Preguntas de prueba
        test_questions = [
            "¿Cuál es la capital de España?",
            "¿Qué es la inteligencia artificial?",
            "Explica brevemente qué es Python",
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\nPregunta {i}: {question}")
            
            try:
                response = await runner.ask_question(question, "test_user")
                print(f"✅ Respuesta recibida ({len(response)} caracteres)")
                print(f"Respuesta: {response[:100]}...")
                
            except Exception as e:
                print(f"❌ Error en pregunta: {e}")
                return False
        
        print("\n✅ Todos los tests básicos pasaron")
        return True
        
    except Exception as e:
        print(f"❌ Error durante los tests: {e}")
        return False


async def main():
    """Función principal de tests"""
    print("Ejecutando tests del agente Q&A...\n")
    
    # Ejecutar test básico
    success = await test_basic_functionality()
    
    if success:
        print("\n🎉 Tests completados exitosamente")
    else:
        print("\n❌ Algunos tests fallaron")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
