# Guía de Instalación del Sistema RAG

Esta guía te ayudará a instalar y configurar el sistema RAG (Retrieval-Augmented Generation) paso a paso.

## Requisitos del Sistema

### Software Requerido

1. **Python 3.11 o superior**
   - Recomendamos usar Conda para gestión de entornos
   - Verificar versión: `python --version`

2. **PostgreSQL 12 o superior**
   - Debe incluir la extensión pgvector
   - Verificar instalación: `psql --version`

3. **Git** (opcional)
   - Para clonar el repositorio
   - Verificar instalación: `git --version`

### Hardware Recomendado

- **RAM**: Mínimo 8GB, recomendado 16GB
- **Almacenamiento**: Al menos 5GB libres
- **CPU**: Procesador multi-core recomendado
- **GPU**: Opcional, acelera el procesamiento de embeddings

## Instalación Paso a Paso

### Paso 1: Preparar el Entorno

```bash
# Crear entorno conda
conda create -n qa_agent python=3.11
conda activate qa_agent

# Verificar que el entorno esté activo
which python
```

### Paso 2: Instalar Dependencias

```bash
# Navegar al directorio del proyecto
cd qa_agent

# Instalar dependencias
pip install -r requirements.txt
```

**Nota**: La instalación puede tomar varios minutos debido a las dependencias de PyTorch y transformers.

### Paso 3: Configurar PostgreSQL

#### Opción A: Instalación Local

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS con Homebrew
brew install postgresql

# Iniciar servicio
sudo systemctl start postgresql  # Linux
brew services start postgresql   # macOS
```

#### Opción B: Docker

```bash
# Ejecutar PostgreSQL con pgvector
docker run -d \
  --name postgres-rag \
  -e POSTGRES_PASSWORD=postgres \
  -e POSTGRES_DB=qa_agent \
  -p 5432:5432 \
  pgvector/pgvector:pg16
```

### Paso 4: Instalar pgvector

```sql
-- Conectar a PostgreSQL
psql -U postgres -d qa_agent

-- Instalar extensión
CREATE EXTENSION IF NOT EXISTS vector;

-- Verificar instalación
\dx vector
```

### Paso 5: Configurar Variables de Entorno

```bash
# Copiar archivo de ejemplo
cp .env.example .env

# Editar configuración
nano .env
```

Configurar las siguientes variables:

```env
# Modelo de LLM
MODEL_NAME=gemini-2.0-flash
GOOGLE_API_KEY=tu_api_key_aqui

# Base de datos
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/qa_agent
DB_HOST=localhost
DB_NAME=qa_agent
DB_USER=postgres
DB_PASSWORD=postgres
DB_PORT=5432

# Embeddings (opcional)
OPENAI_API_KEY=tu_openai_key_aqui
```

### Paso 6: Inicializar el Sistema

```bash
# Cargar documentos iniciales
python src/main.py --init

# Probar el sistema
python src/main.py --test-search "¿Cómo instalar el sistema?"
```

## Configuración Avanzada

### Modelos de Embeddings

#### OpenAI (Recomendado)
- Requiere API key de OpenAI
- Mejor calidad de embeddings
- Costo por uso

```env
OPENAI_API_KEY=tu_key_aqui
```

#### SentenceTransformers (Gratuito)
- Modelo local, sin API key
- Buena calidad, menor costo
- Requiere más recursos locales

```python
# En src/embeddings.py
embedding_generator = EmbeddingGenerator(
    provider="sentence_transformers",
    model_name="all-MiniLM-L6-v2"
)
```

### Optimización de Base de Datos

```sql
-- Configurar índices para mejor rendimiento
CREATE INDEX CONCURRENTLY idx_chunks_embedding_cosine 
ON agente_v5.document_chunks 
USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- Configurar parámetros de memoria
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
```

### Configuración de Chunking

```python
# En src/document_processor.py
processor = DocumentProcessor(
    chunk_size=1000,      # Tamaño de chunk en tokens
    chunk_overlap=200     # Solapamiento entre chunks
)
```

## Solución de Problemas Comunes

### Error: "No module named 'pgvector'"

```bash
# Reinstalar pgvector
pip uninstall pgvector
pip install pgvector
```

### Error: "Connection refused" (PostgreSQL)

```bash
# Verificar que PostgreSQL esté ejecutándose
sudo systemctl status postgresql

# Iniciar si está detenido
sudo systemctl start postgresql
```

### Error: "API key not found"

1. Verificar que el archivo `.env` existe
2. Confirmar que las variables están configuradas
3. Reiniciar el terminal después de cambios

### Error: "Out of memory" durante instalación

```bash
# Usar instalación por partes
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install sentence-transformers
pip install -r requirements.txt
```

### Error: "Permission denied" en PostgreSQL

```sql
-- Otorgar permisos al usuario
GRANT ALL PRIVILEGES ON DATABASE qa_agent TO postgres;
GRANT ALL ON SCHEMA agente_v5 TO postgres;
```

## Verificación de Instalación

### Test Completo

```bash
# Ejecutar todos los tests
python src/main.py --init --force-reload
python src/main.py --test-search "¿Qué es RAG?"
python src/main.py --chat
```

### Verificar Componentes

```python
# Test de base de datos
from src.database import db_manager
await db_manager.initialize()

# Test de embeddings
from src.embeddings import embedding_generator
embedding = await embedding_generator.generate_embedding("test")

# Test de procesamiento
from src.document_processor import document_processor
docs = await document_processor.process_documents_folder(Path("docs"))
```

## Mantenimiento

### Actualizar Documentos

```bash
# Agregar nuevos documentos a la carpeta docs/
cp nuevo_documento.pdf docs/

# Recargar sistema
python src/main.py --init --force-reload
```

### Backup de Base de Datos

```bash
# Backup completo
pg_dump -U postgres qa_agent > backup_qa_agent.sql

# Restaurar backup
psql -U postgres qa_agent < backup_qa_agent.sql
```

### Monitoreo de Rendimiento

```sql
-- Verificar uso de índices
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE schemaname = 'agente_v5';

-- Verificar tamaño de tablas
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'agente_v5';
```

## Próximos Pasos

Una vez completada la instalación:

1. **Agregar documentos**: Coloca tus documentos en la carpeta `docs/`
2. **Configurar el agente**: Personaliza las instrucciones en `src/agent.py`
3. **Probar la interfaz web**: Ejecuta `adk web` para usar la interfaz gráfica
4. **Optimizar rendimiento**: Ajusta parámetros según tu uso específico

¡Tu sistema RAG está listo para usar!
